{"name": "insolventic", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "postinstall": "prisma generate", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "prepare": "husky", "db:seed": "prisma db seed", "db:reset": "tsx src/prisma/reset.ts", "db:reset:seed": "tsx src/prisma/reset.ts --seed"}, "prisma": {"schema": "src/prisma/schema.prisma", "seed": "tsx src/prisma/seed.ts"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,scss,md,json}": ["prettier --write"]}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-aspect-ratio": "1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "1.1.11", "@radix-ui/react-context-menu": "2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "1.1.15", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "1.2.14", "@radix-ui/react-toggle": "1.1.9", "@radix-ui/react-toggle-group": "1.1.10", "@radix-ui/react-tooltip": "1.2.7", "@tinymce/tinymce-react": "^6.2.1", "autoprefixer": "^10.4.21", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "4.1.0", "docx": "^9.5.1", "docx-preview": "^0.3.5", "docx-templates": "^4.14.1", "dotenv": "^17.2.0", "embla-carousel-react": "8.6.0", "file-saver": "^2.0.5", "googleapis": "^153.0.0", "html-docx-js": "^0.3.1", "input-otp": "1.4.2", "jszip": "^3.10.1", "lucide-react": "^0.525.0", "mammoth": "^1.9.1", "next": "15.3.5", "next-themes": "^0.4.6", "react": "^19", "react-day-picker": "9.8.0", "react-doc-viewer": "^0.1.14", "react-dom": "^19", "react-hook-form": "^7.60.0", "react-resizable-panels": "^3.0.3", "recharts": "3.0.2", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.74", "zsa": "^0.6.0", "zsa-react": "^0.2.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/bcrypt": "^5.0.2", "@types/file-saver": "^2.0.7", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "9.30.1", "eslint-config-next": "15.3.5", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "prisma": "^6.11.1", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "typescript": "^5"}}