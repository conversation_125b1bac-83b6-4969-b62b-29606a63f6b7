import { NextRequest, NextResponse } from 'next/server';
import mammoth from 'mammoth';
import { downloadDocumentTemplate } from '@/features/document/actions';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const [result] = await downloadDocumentTemplate({ id });

    if (!result) {
      return NextResponse.json(
        { error: 'Plantilla no encontrada' },
        { status: 404 },
      );
    }

    const { buffer, fileName } = result;

    // Convertir documento Word a HTML usando mammoth
    const htmlResult = await mammoth.convertToHtml({ buffer });

    // Crear HTML completo con estilos básicos
    const fullHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Vista previa: ${fileName}</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              line-height: 1.6;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
              background-color: #f5f5f5;
            }
            .document-container {
              background: white;
              padding: 40px;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              min-height: 600px;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              padding-bottom: 20px;
              border-bottom: 2px solid #e0e0e0;
            }
            .header h1 {
              color: #333;
              margin: 0;
              font-size: 24px;
            }
            .header p {
              color: #666;
              margin: 5px 0 0 0;
              font-style: italic;
            }
            .content {
              color: #333;
            }
            .content p {
              margin-bottom: 12px;
            }
            .content h1, .content h2, .content h3 {
              color: #2c3e50;
              margin-top: 25px;
              margin-bottom: 15px;
            }
            .content table {
              width: 100%;
              border-collapse: collapse;
              margin: 20px 0;
            }
            .content table th,
            .content table td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: left;
            }
            .content table th {
              background-color: #f2f2f2;
              font-weight: bold;
            }
            .placeholder {
              background-color: #fff3cd;
              border: 1px solid #ffeaa7;
              padding: 2px 6px;
              border-radius: 3px;
              font-weight: bold;
              color: #856404;
            }
            .footer {
              margin-top: 40px;
              padding-top: 20px;
              border-top: 1px solid #e0e0e0;
              text-align: center;
              color: #666;
              font-size: 12px;
            }
          </style>
        </head>
        <body>
          <div class="document-container">
            <div class="header">
              <h1>Vista Previa de Plantilla</h1>
              <p>${fileName}</p>
            </div>
            <div class="content">
              ${htmlResult.value}
            </div>
            <div class="footer">
              <p>Esta es una vista previa del documento. Los placeholders se reemplazarán con datos reales al generar documentos.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    return new NextResponse(fullHtml, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  } catch (error) {
    console.error('Error generando vista previa:', error);

    // HTML de error
    const errorHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Error en vista previa</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              text-align: center;
              padding: 50px;
              background-color: #f5f5f5;
            }
            .error-container {
              background: white;
              padding: 40px;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              max-width: 500px;
              margin: 0 auto;
            }
            .error-icon {
              font-size: 48px;
              color: #e74c3c;
              margin-bottom: 20px;
            }
            h1 {
              color: #333;
              margin-bottom: 10px;
            }
            p {
              color: #666;
              line-height: 1.6;
            }
          </style>
        </head>
        <body>
          <div class="error-container">
            <div class="error-icon">⚠️</div>
            <h1>Error al generar vista previa</h1>
            <p>No se pudo generar la vista previa del documento. Esto puede ocurrir si el archivo no es un documento Word válido o si hay problemas de conectividad.</p>
            <p>Puedes intentar descargar el archivo directamente para verificar su contenido.</p>
          </div>
        </body>
      </html>
    `;

    return new NextResponse(errorHtml, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
      },
      status: 500,
    });
  }
}
