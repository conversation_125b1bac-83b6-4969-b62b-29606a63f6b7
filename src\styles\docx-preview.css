/* Styles for docx-preview component */
.docx-container {
  font-family: 'Times New Roman', serif;
  line-height: 1.6;
  color: #333;
}

/* Override default docx-preview styles */
.docx-preview {
  font-family: inherit;
  line-height: inherit;
  color: inherit;
}

.docx-preview .docx-wrapper {
  background: transparent;
  box-shadow: none;
  border: none;
  margin: 0;
  padding: 0;
}

.docx-preview .docx-wrapper > section {
  background: white;
  margin: 0;
  padding: 20px;
  box-shadow: none;
  border: none;
}

/* Table styles */
.docx-preview table {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0;
}

.docx-preview table td,
.docx-preview table th {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.docx-preview table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

/* Paragraph styles */
.docx-preview p {
  margin: 6px 0;
  text-align: justify;
}

/* Header styles */
.docx-preview h1,
.docx-preview h2,
.docx-preview h3,
.docx-preview h4,
.docx-preview h5,
.docx-preview h6 {
  margin: 12px 0 6px 0;
  font-weight: bold;
}

/* List styles */
.docx-preview ul,
.docx-preview ol {
  margin: 6px 0;
  padding-left: 20px;
}

.docx-preview li {
  margin: 3px 0;
}

/* Image styles */
.docx-preview img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 10px auto;
}

/* Page break styles */
.docx-preview .docx-page-break {
  page-break-before: always;
  break-before: page;
}

/* Footer and header styles */
.docx-preview .docx-header,
.docx-preview .docx-footer {
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
  padding: 10px 0;
  margin: 10px 0;
  font-size: 0.9em;
  color: #666;
}

/* Placeholder highlighting for templates */
.docx-preview .placeholder {
  background-color: #fff3cd;
  border: 1px dashed #ffc107;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: bold;
  color: #856404;
}

/* Print styles */
@media print {
  .docx-container {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  .docx-preview .docx-wrapper > section {
    padding: 0;
    margin: 0;
    box-shadow: none;
    border: none;
  }
}
