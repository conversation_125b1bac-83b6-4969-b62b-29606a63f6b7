'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { X, Download, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { DocumentTemplate } from '@/features/document/types';
import * as docx from 'docx-preview';

interface DocumentPreviewDialogProps {
  template: DocumentTemplate | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DocumentPreviewDialog({
  template,
  open,
  onOpenChange,
}: Readonly<DocumentPreviewDialogProps>) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const loadPreview = useCallback(async () => {
    if (!template || !containerRef.current) return;

    setIsLoading(true);
    setError(null);

    try {
      // Check if the file is a Word document
      const isWordDocument =
        template.mimeType ===
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
        template.mimeType === 'application/msword' ||
        template.fileName.toLowerCase().endsWith('.docx') ||
        template.fileName.toLowerCase().endsWith('.doc');

      if (!isWordDocument) {
        throw new Error(
          'Este tipo de archivo no es compatible con la vista previa directa. Solo se admiten documentos Word (.docx, .doc).',
        );
      }

      // Download the DOCX file as ArrayBuffer
      const response = await fetch(`/api/templates/${template.id}/download`);

      if (!response.ok) {
        throw new Error('Error al cargar el documento');
      }

      const arrayBuffer = await response.arrayBuffer();

      // Clear the container
      containerRef.current.innerHTML = '';

      // Render the DOCX document directly using docx-preview
      await docx.renderAsync(arrayBuffer, containerRef.current, undefined, {
        className: 'docx-preview',
        inWrapper: true,
        ignoreWidth: false,
        ignoreHeight: false,
        ignoreFonts: false,
        breakPages: true,
        ignoreLastRenderedPageBreak: true,
        experimental: false,
        trimXmlDeclaration: true,
        useBase64URL: false,
        renderChanges: false,
        renderHeaders: true,
        renderFooters: true,
        renderFootnotes: true,
        renderEndnotes: true,
        renderComments: false,
        debug: false,
      });
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Error al cargar el documento',
      );
    } finally {
      setIsLoading(false);
    }
  }, [template]);

  useEffect(() => {
    if (open && template) {
      loadPreview();
    } else {
      // Clear the container when dialog closes
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
      setError(null);
    }
  }, [open, template, loadPreview]);

  const handleDownload = () => {
    if (template) {
      const url = `/api/templates/${template.id}/download`;
      const link = document.createElement('a');
      link.href = url;
      link.download = template.fileName || `${template.name}.docx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  if (!template) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent
        className="max-h-[90vh] w-[90vw] overflow-hidden sm:max-w-6xl"
        showCloseButton={false}
      >
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg font-semibold">
              Vista Previa: {template.name}
            </DialogTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                disabled={isLoading}
              >
                <Download className="mr-2 h-4 w-4" />
                Descargar
              </Button>
              <Button variant="ghost" size="sm" onClick={handleClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex flex-col gap-4 overflow-hidden">
          {isLoading && (
            <div className="flex h-96 items-center justify-center">
              <div className="text-center">
                <Loader2 className="mx-auto h-8 w-8 animate-spin text-blue-600" />
                <p className="mt-2 text-sm text-gray-600">
                  Cargando vista previa...
                </p>
              </div>
            </div>
          )}

          {error && (
            <div className="flex h-96 items-center justify-center">
              <div className="text-center">
                <p className="font-medium text-red-600">Error</p>
                <p className="mt-1 text-sm text-gray-600">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadPreview}
                  className="mt-4"
                >
                  Reintentar
                </Button>
              </div>
            </div>
          )}

          {!isLoading && !error && (
            <>
              <div className="max-h-[60vh] flex-1 overflow-y-auto rounded-lg border bg-white">
                <div ref={containerRef} className="docx-container p-4" />
              </div>
              <div className="rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 font-medium text-gray-900">
                  Información del Documento
                </h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Nombre:</span>
                    <span className="ml-2 text-gray-600">{template.name}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">
                      Categoría:
                    </span>
                    <span className="ml-2 text-gray-600">
                      {template.category}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Archivo:</span>
                    <span className="ml-2 text-gray-600">
                      {template.fileName}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">
                      Placeholders:
                    </span>
                    <span className="ml-2 text-gray-600">
                      {template.placeholders?.length || 0} campos
                    </span>
                  </div>
                </div>
                {template.description && (
                  <div className="mt-3">
                    <span className="font-medium text-gray-700">
                      Descripción:
                    </span>
                    <p className="mt-1 text-gray-600">{template.description}</p>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
